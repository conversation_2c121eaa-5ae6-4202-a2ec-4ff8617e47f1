package ui

import (
    "fyne.io/fyne/v2"
    "fyne.io/fyne/v2/container"
    "fyne.io/fyne/v2/widget"
)

type Tool struct {
    Name        string
    Description string
    Icon        fyne.Resource
    MakeUI      func() fyne.CanvasObject
}

func MakeToolsGrid(onToolSelect func(fyne.CanvasObject)) fyne.CanvasObject {
    tools := []Tool{
        {
            Name:        "JSON Formatter",
            Description: "Formate et valide du JSON",
            MakeUI:      MakeJSONFormatterUI,
        },
        {
            Name:        "Text Splitter",
            Description: "Divise du texte selon un délimiteur",
            MakeUI:      MakeTextSplitterUI,
        },
        {
            Name:        "Text Joiner",
            Description: "Joint du texte avec un délimiteur",
            MakeUI:      MakeTextJoinerUI,
        },
    }

    grid := container.NewGridWithColumns(3)

    for _, tool := range tools {
        toolCopy := tool // Capture pour la closure
        
        card := widget.NewCard(toolCopy.Name, toolCopy.Description, 
            widget.NewButton("Ouvrir", func() {
                onToolSelect(toolCopy.MakeUI())
            }))
        
        grid.Add(card)
    }

    return container.NewVBox(
        widget.NewLabel("Sélectionnez un outil"),
        container.NewScroll(grid),
    )
}